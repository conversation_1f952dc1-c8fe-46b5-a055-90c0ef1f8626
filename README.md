# تطبيق إنشاء خطط الأعمال 📊

تطبيق ويب متكامل لمساعدة رواد الأعمال على إنشاء خطط أعمال احترافية خطوة بخطوة باللغة العربية.

## ✨ المميزات الجديدة

### 🔄 إدارة البيانات المركزية
- **حفظ تلقائي**: يتم حفظ البيانات تلقائياً عند كل تغيير
- **استرداد البيانات**: يتم استرداد البيانات المحفوظة عند إعادة فتح التطبيق
- **التحقق من صحة البيانات**: فحص البيانات المطلوبة قبل الانتقال بين الخطوات

### 📊 حسابات مالية تفاعلية
- **تحديث فوري**: تتحدث جميع الحسابات المالية تلقائياً عند تغيير البيانات
- **نقطة التعادل**: حساب نقطة التعادل بناءً على التكاليف والأسعار
- **تحليل الربحية**: عرض تفصيلي للأرباح والخسائر

### 🎯 تتبع التقدم المحسن
- **شريط تقدم تفاعلي**: يظهر حالة كل خطوة (مكتملة، حالية، غير مكتملة)
- **تنبيهات البيانات**: تحذيرات عند وجود بيانات ناقصة
- **ملخص المشروع**: عرض ملخص شامل في النهاية

### 💾 وظائف الحفظ والتصدير
- **حفظ محلي**: حفظ البيانات في متصفح المستخدم
- **تصدير PDF**: (قريباً) تصدير خطة العمل كملف PDF
- **تصدير Excel**: (قريباً) تصدير البيانات المالية كجدول بيانات

## 🏗️ البنية التقنية

### التقنيات المستخدمة
- **React 18** + **TypeScript** للواجهة الأمامية
- **Vite** كأداة بناء سريعة
- **Tailwind CSS** للتصميم
- **Shadcn/UI** لمكونات الواجهة
- **React Hook Form** + **Zod** للنماذج والتحقق
- **React Query** لإدارة الحالة
- **Context API** لإدارة البيانات المركزية

### هيكل المشروع
```
src/
├── components/
│   ├── ui/                    # مكونات Shadcn/UI
│   ├── project-form/          # نماذج المشروع
│   ├── wizard-steps/          # خطوات المعالج
│   └── ProjectFormWizard.tsx  # المعالج الرئيسي
├── contexts/
│   └── ProjectContext.tsx     # إدارة البيانات المركزية
├── types/
│   └── project.ts            # أنواع البيانات
├── pages/
│   ├── Index.tsx             # الصفحة الرئيسية
│   └── NotFound.tsx          # صفحة 404
└── App.tsx                   # التطبيق الرئيسي
```

## 🚀 التشغيل

```bash
# تثبيت التبعيات
npm install

# تشغيل التطبيق في وضع التطوير
npm run dev

# بناء التطبيق للإنتاج
npm run build
```

## 📋 خطوات إنشاء خطة العمل

1. **المعلومات الشخصية ووصف المشروع**
   - بيانات صاحب المشروع
   - وصف شامل للمشروع وأهدافه

2. **دراسة السوق والمنافسين**
   - تحليل المنتجات والخدمات
   - دراسة المنافسة والأسعار

3. **التحليل الرباعي (SWOT)**
   - نقاط القوة والضعف
   - الفرص والتهديدات

4. **المزيج التسويقي (4Ps + 1)**
   - المنتج، السعر، المكان، الترويج، الأشخاص

5. **مستلزمات الإنتاج**
   - المعدات والمواد المطلوبة
   - الموارد البشرية والتراخيص

6. **الدراسة المالية**
   - التكاليف الثابتة والمتغيرة
   - تحليل الربحية ونقطة التعادل
   - توقعات المبيعات السنوية

7. **الإنهاء والحفظ**
   - ملخص المشروع
   - خيارات الحفظ والتصدير

## 🔧 التحسينات المضافة

### إدارة البيانات
- ✅ نظام إدارة بيانات مركزي باستخدام Context API
- ✅ حفظ تلقائي في Local Storage
- ✅ استرداد البيانات عند إعادة التحميل
- ✅ التحقق من صحة البيانات لكل خطوة

### تجربة المستخدم
- ✅ تنبيهات وإشعارات تفاعلية
- ✅ شريط تقدم محسن مع حالة الخطوات
- ✅ منع الانتقال مع بيانات ناقصة
- ✅ ملخص شامل للمشروع

### الحسابات المالية
- ✅ تحديث تلقائي للحسابات
- ✅ حساب نقطة التعادل
- ✅ تحليل الربحية التفاعلي
- ✅ جداول مالية شاملة

## 🎯 الميزات القادمة

- 📄 تصدير PDF احترافي
- 📊 تصدير Excel مع الرسوم البيانية
- 🌐 حفظ سحابي
- 📱 تطبيق موبايل
- 🤖 مساعد ذكي لتحليل البيانات

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل معنا.

---

**تم تطوير هذا التطبيق باستخدام Dyad Platform** 🚀
