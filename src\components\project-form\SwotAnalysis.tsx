import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { ThumbsUp, ThumbsDown, Lightbulb, AlertTriangle, Target } from "lucide-react";
import { useProject } from "@/contexts/ProjectContext";
import React from "react";

const SwotField = ({
    title,
    description,
    id,
    icon: Icon,
    color
}: {
    title: string;
    description: string;
    id: keyof import("@/types/project").SwotAnalysis;
    icon: React.ElementType;
    color: string;
}) => {
    const { projectData, updateSwotAnalysis } = useProject();
    const value = projectData.swotAnalysis[id];

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        updateSwotAnalysis({ [id]: e.target.value });
    };

    return (
        <div className="space-y-3 p-4 rounded-lg border bg-card shadow-sm">
            <div className="flex items-center gap-3">
                <div className={cn("flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full", color)}>
                    <Icon className="h-6 w-6 text-white" />
                </div>
                <Label htmlFor={id} className="font-semibold text-xl">{title}</Label>
            </div>
            <p className="text-sm text-muted-foreground pr-14">{description}</p>
            <Textarea
                id={id}
                rows={5}
                className="mt-2"
                value={value || ''}
                onChange={handleChange}
            />
        </div>
    );
};

export const SwotAnalysis = () => {
  return (
    <Card className="w-full bg-amber-50 dark:bg-amber-900/30">
      <CardHeader className="border-b">
        <CardTitle className="flex items-center gap-3 text-2xl font-bold bg-gradient-to-r from-primary via-blue-500 to-sky-400 text-transparent bg-clip-text">
            <Target className="h-6 w-6 text-primary" />
            التحليل الرباعي – SWOT Analysis
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <SwotField 
                title="نقاط القوة"
                description="(المهارات، القدرة المالية، الدعم العائلي، ...)"
                id="strengths"
                icon={ThumbsUp}
                color="bg-green-500"
            />
            <SwotField 
                title="نقاط الضعف"
                description="(عدم وجود المهارات، ضعف القدرة المالية، ...)"
                id="weaknesses"
                icon={ThumbsDown}
                color="bg-red-500"
            />
            <SwotField 
                title="الفرص"
                description="(عوامل اقتصادية، قانونية، اجتماعية إيجابية...)"
                id="opportunities"
                icon={Lightbulb}
                color="bg-blue-500"
            />
            <SwotField 
                title="التهديدات"
                description="(عوامل خارجية قد تضر المشروع، منافسة شرسة، ...)"
                id="threats"
                icon={AlertTriangle}
                color="bg-yellow-500"
            />
        </div>
      </CardContent>
    </Card>
  );
};