import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { User } from "lucide-react";
import { useProject } from "@/contexts/ProjectContext";

interface FormFieldProps {
  label: string;
  id: keyof import("@/types/project").PersonalInfo;
  type?: string;
}

const FormField = ({ label, id, type = "text" }: FormFieldProps) => {
  const { projectData, updatePersonalInfo } = useProject();
  const value = projectData.personalInfo[id];

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = type === "number" ? Number(e.target.value) : e.target.value;
    updatePersonalInfo({ [id]: newValue });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 items-center gap-4">
      <Label htmlFor={id} className="md:text-right">{label}</Label>
      <Input
        id={id}
        type={type}
        value={value || ''}
        onChange={handleChange}
        className="col-span-1 md:col-span-2"
      />
    </div>
  );
};

export const PersonalInfo = () => {
  return (
    <Card className="w-full bg-sky-50 dark:bg-sky-900/30">
      <CardHeader className="border-b">
        <CardTitle className="flex items-center gap-3 text-2xl font-bold bg-gradient-to-r from-primary via-blue-500 to-sky-400 text-transparent bg-clip-text">
          <User className="h-6 w-6 text-primary" />
          المعلومات الشخصية
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="space-y-4">
          <FormField label="إسم صاحب/ة المشروع" id="ownerName" />
          <FormField label="العمر" id="age" type="number" />
          <FormField label="الحالة الإجتماعية" id="maritalStatus" />
          <FormField label="عدد أفراد الأسرة" id="familySize" type="number" />
          <FormField label="المؤهل العلمي" id="education" />
          <FormField label="رقم الهاتف" id="phone" />
          <FormField label="رقم هاتف شخص معرف" id="refereePhone" />
          <FormField label="مكان السكن" id="address" />
        </div>
      </CardContent>
    </Card>
  );
};