import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { FileText } from "lucide-react";
import { useProject } from "@/contexts/ProjectContext";

interface FormFieldProps {
  label: string;
  id: keyof import("@/types/project").ProjectDescription;
  children?: React.ReactNode;
  type?: string;
}

const FormField = ({ label, id, children, type = "text" }: FormFieldProps) => {
  const { projectData, updateProjectDescription } = useProject();

  if (children) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 items-start gap-4">
        <Label htmlFor={id} className="md:text-right pt-2">{label}</Label>
        <div className="col-span-1 md:col-span-2">{children}</div>
      </div>
    );
  }

  const value = projectData.projectDescription[id];

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const newValue = type === "number" ? Number(e.target.value) : e.target.value;
    updateProjectDescription({ [id]: newValue });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 items-start gap-4">
      <Label htmlFor={id} className="md:text-right pt-2">{label}</Label>
      <div className="col-span-1 md:col-span-2">
        {type === "textarea" ? (
          <Textarea
            id={id}
            value={value || ''}
            onChange={handleChange}
          />
        ) : (
          <Input
            id={id}
            type={type}
            value={value || ''}
            onChange={handleChange}
          />
        )}
      </div>
    </div>
  );
};

const CheckboxField = ({ label, id }: { label: string; id: string }) => (
    <Label htmlFor={id} className="font-normal flex items-center gap-2 cursor-pointer">
        <span>{label}</span>
        <Checkbox id={id} />
    </Label>
);

const RadioField = ({ label, id, value }: { label: string; id: string; value: string }) => (
    <Label htmlFor={id} className="font-normal flex items-center gap-2 cursor-pointer">
        <span>{label}</span>
        <RadioGroupItem value={value} id={id} />
    </Label>
);

export const ProjectDescription = () => {
  return (
    <Card className="w-full bg-blue-50 dark:bg-blue-900/30">
      <CardHeader className="border-b">
        <CardTitle className="flex items-center gap-3 text-2xl font-bold bg-gradient-to-r from-primary via-blue-500 to-sky-400 text-transparent bg-clip-text">
          <FileText className="h-6 w-6 text-primary" />
          وصف المشروع
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="space-y-6">
          <FormField label="ملخص وصف خصائص المشروع" id="projectSummary" type="textarea" />
          <FormField label="اسم المشروع" id="projectName" />
          <FormField label="موقع المشروع" id="projectLocation" />
          <FormField label="قيمة المنحة المطلوبة" id="grantAmount">
            <Input id="grantAmount" type="number" />
          </FormField>
          <FormField label="التمويل الذاتي أو مصادر التمويل الأخرى المتوفرة" id="selfFunding">
            <Input id="selfFunding" />
          </FormField>
          <FormField label="تكلفة المشروع الكلية" id="totalCost">
            <Input id="totalCost" type="number" />
          </FormField>
          <FormField label="تاريخ تقديم خطة المشروع" id="submissionDate">
            <Input id="submissionDate" type="date" />
          </FormField>
          <FormField label="وصف أهمية الفكرة ونوع المشروع" id="ideaDescription">
            <Textarea id="ideaDescription" />
          </FormField>
          <FormField label="وصف المهارات اللازم امتلاكها لتنفيذ المشروع" id="skillsDescription">
            <Textarea id="skillsDescription" />
          </FormField>
          <FormField label="وصف حاجة المجتمع للمشروع" id="communityNeed">
            <Textarea id="communityNeed" />
          </FormField>
          <FormField label="هل يحتاج المشروع إلى ترخيص؟" id="licenseRequired">
            <RadioGroup defaultValue="no" className="flex items-center gap-x-6">
              <RadioField label="نعم" id="licenseYes" value="yes" />
              <RadioField label="لا" id="licenseNo" value="no" />
            </RadioGroup>
          </FormField>
          <FormField label="إذا كان نعم، أذكر جهة الترخيص" id="licensingAuthority">
            <Input id="licensingAuthority" />
          </FormField>
          <FormField label="الفئة المستهدفة بالمشروع" id="targetAudience">
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
              <CheckboxField label="أطفال" id="targetChildren" />
              <CheckboxField label="شباب" id="targetYouth" />
              <CheckboxField label="نساء" id="targetWomen" />
              <CheckboxField label="رجال" id="targetMen" />
              <CheckboxField label="كبار سن" id="targetSeniors" />
              <CheckboxField label="مؤسسات/شركات" id="targetCompanies" />
              <CheckboxField label="جمعيات" id="targetAssociations" />
              <CheckboxField label="مدارس" id="targetSchools" />
              <CheckboxField label="فنادق" id="targetHotels" />
              <div className="flex items-center col-span-2 sm:col-span-3 gap-2">
                <Label htmlFor="targetOther" className="font-normal flex items-center gap-2 cursor-pointer">
                    <span>أخرى:</span>
                    <Checkbox id="targetOther" />
                </Label>
                <Input id="targetOtherText" className="flex-1" />
              </div>
            </div>
          </FormField>
        </div>
      </CardContent>
    </Card>
  );
};