import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, ArrowRight, CheckCircle, AlertCircle } from "lucide-react";
import { FormProgress } from "./FormProgress";
import { useProject } from "@/contexts/ProjectContext";
import { toast } from "@/hooks/use-toast";

// استيراد مكونات الخطوات
import { Step1_Intro } from "./wizard-steps/Step1_Intro";
import { Step2_MarketAnalysis } from "./wizard-steps/Step2_MarketAnalysis";
import { Step3_SwotAnalysis } from "./wizard-steps/Step3_SwotAnalysis";
import { Step4_MarketingMix } from "./wizard-steps/Step4_MarketingMix";
import { Step5_ProductionReqs } from "./wizard-steps/Step5_ProductionReqs";
import { Step6_FinancialStudy } from "./wizard-steps/Step6_FinancialStudy";
import { Step7_Final } from "./wizard-steps/Step7_Final";

const steps = [
  { id: 1, title: "المعلومات الشخصية ووصف المشروع", component: <Step1_Intro /> },
  { id: 2, title: "دراسة السوق والمنافسين", component: <Step2_MarketAnalysis /> },
  { id: 3, title: "التحليل الرباعي (SWOT)", component: <Step3_SwotAnalysis /> },
  { id: 4, title: "المزيج التسويقي (4Ps + 1)", component: <Step4_MarketingMix /> },
  { id: 5, title: "مستلزمات الإنتاج", component: <Step5_ProductionReqs /> },
  { id: 6, title: "الدراسة المالية", component: <Step6_FinancialStudy /> },
  { id: 7, title: "حفظ وتصدير", component: <Step7_Final /> },
];

export const ProjectFormWizard = () => {
  const { projectData, setCurrentStep, isDataValid, saveProject } = useProject();
  const currentStep = projectData.currentStep;

  const goToNext = () => {
    // التحقق من صحة البيانات قبل الانتقال
    if (!isDataValid(currentStep)) {
      toast({
        title: "بيانات غير مكتملة",
        description: "يرجى إكمال البيانات المطلوبة قبل الانتقال للخطوة التالية",
        variant: "destructive",
      });
      return;
    }

    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
      toast({
        title: "تم الحفظ",
        description: "تم حفظ بيانات الخطوة الحالية تلقائياً",
      });
    }
  };

  const goToPrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleFinish = () => {
    if (!isDataValid(currentStep)) {
      toast({
        title: "بيانات غير مكتملة",
        description: "يرجى إكمال البيانات المطلوبة قبل الإنهاء",
        variant: "destructive",
      });
      return;
    }

    saveProject();
    toast({
      title: "تم إنهاء المشروع بنجاح!",
      description: "تم حفظ جميع بيانات خطة العمل",
    });
  };

  const CurrentComponent = steps[currentStep].component;

  return (
    <div className="w-full max-w-5xl mx-auto space-y-8">
      <FormProgress currentStep={currentStep} totalSteps={steps.length} />
      <Card>
        <CardHeader>
          <CardTitle>{steps[currentStep].title}</CardTitle>
          <CardDescription>الخطوة {currentStep + 1} من {steps.length}</CardDescription>
        </CardHeader>
        <CardContent>
          {CurrentComponent}
        </CardContent>
        <CardFooter className="flex justify-between items-center">
          <Button onClick={goToPrevious} disabled={currentStep === 0} variant="outline">
            <ArrowRight className="ml-2 h-4 w-4" />
            السابق
          </Button>

          <div className="flex items-center gap-2">
            {!isDataValid(currentStep) && (
              <div className="flex items-center gap-1 text-amber-600">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">بيانات غير مكتملة</span>
              </div>
            )}
          </div>

          {currentStep < steps.length - 1 ? (
            <Button onClick={goToNext}>
              التالي
              <ArrowLeft className="mr-2 h-4 w-4" />
            </Button>
          ) : (
            <Button onClick={handleFinish} className="bg-green-600 hover:bg-green-700">
              إنهاء وحفظ
              <CheckCircle className="mr-2 h-4 w-4" />
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
};