import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, ArrowRight, CheckCircle } from "lucide-react";
import { FormProgress } from "./FormProgress";

// استيراد مكونات الخطوات
import { Step1_Intro } from "./wizard-steps/Step1_Intro";
import { Step2_MarketAnalysis } from "./wizard-steps/Step2_MarketAnalysis";
import { Step3_SwotAnalysis } from "./wizard-steps/Step3_SwotAnalysis";
import { Step4_MarketingMix } from "./wizard-steps/Step4_MarketingMix";
import { Step5_ProductionReqs } from "./wizard-steps/Step5_ProductionReqs";
import { Step6_FinancialStudy } from "./wizard-steps/Step6_FinancialStudy";
import { Step7_Final } from "./wizard-steps/Step7_Final";

const steps = [
  { id: 1, title: "المعلومات الشخصية ووصف المشروع", component: <Step1_Intro /> },
  { id: 2, title: "دراسة السوق والمنافسين", component: <Step2_MarketAnalysis /> },
  { id: 3, title: "التحليل الرباعي (SWOT)", component: <Step3_SwotAnalysis /> },
  { id: 4, title: "المزيج التسويقي (4Ps + 1)", component: <Step4_MarketingMix /> },
  { id: 5, title: "مستلزمات الإنتاج", component: <Step5_ProductionReqs /> },
  { id: 6, title: "الدراسة المالية", component: <Step6_FinancialStudy /> },
  { id: 7, title: "حفظ وتصدير", component: <Step7_Final /> },
];

export const ProjectFormWizard = () => {
  const [currentStep, setCurrentStep] = useState(0);

  const goToNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const goToPrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const CurrentComponent = steps[currentStep].component;

  return (
    <div className="w-full max-w-5xl mx-auto space-y-8">
      <FormProgress currentStep={currentStep} totalSteps={steps.length} />
      <Card>
        <CardHeader>
          <CardTitle>{steps[currentStep].title}</CardTitle>
          <CardDescription>الخطوة {currentStep + 1} من {steps.length}</CardDescription>
        </CardHeader>
        <CardContent>
          {CurrentComponent}
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button onClick={goToPrevious} disabled={currentStep === 0} variant="outline">
            <ArrowRight className="ml-2 h-4 w-4" />
            السابق
          </Button>
          {currentStep < steps.length - 1 ? (
            <Button onClick={goToNext}>
              التالي
              <ArrowLeft className="mr-2 h-4 w-4" />
            </Button>
          ) : (
            <Button className="bg-green-600 hover:bg-green-700">
              إنهاء وحفظ
              <CheckCircle className="mr-2 h-4 w-4" />
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
};