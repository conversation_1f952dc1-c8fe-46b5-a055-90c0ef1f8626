import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Wrench } from "lucide-react";

const RequirementsTable = ({ title, example }: { title: string; example: string; }) => {
    // In a real app, this would be state managed with useState
    const rows = [
        { id: 1, item: "", unitPrice: "", quantity: "", total: "" },
        { id: 2, item: "", unitPrice: "", quantity: "", total: "" },
        { id: 3, item: "", unitPrice: "", quantity: "", total: "" },
    ];

    return (
        <div className="space-y-4">
            <h3 className="text-lg font-semibold">{title}</h3>
            <p className="text-sm text-muted-foreground">مثال: {example}</p>
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[40%]">البند</TableHead>
                            <TableHead>سعر الوحدة</TableHead>
                            <TableHead>عدد الوحدات</TableHead>
                            <TableHead className="text-left">المجموع</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {rows.map(row => (
                            <TableRow key={row.id}>
                                <TableCell><Input placeholder="اسم البند..." /></TableCell>
                                <TableCell><Input type="number" placeholder="0.00" /></TableCell>
                                <TableCell><Input type="number" placeholder="0" /></TableCell>
                                <TableCell className="text-left"><Input readOnly placeholder="0.00" /></TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                    <TableFooter>
                        <TableRow>
                            <TableCell colSpan={3} className="font-bold">الإجمالي الكلي</TableCell>
                            <TableCell className="text-left font-bold">0.00</TableCell>
                        </TableRow>
                    </TableFooter>
                </Table>
            </div>
            <Button variant="outline" size="sm">إضافة صف جديد</Button>
        </div>
    );
};

export const ProductionRequirements = () => {
  return (
    <Card className="w-full bg-stone-50 dark:bg-stone-900/30">
      <CardHeader className="border-b">
        <CardTitle className="flex items-center gap-3 text-2xl font-bold bg-gradient-to-r from-primary via-blue-500 to-sky-400 text-transparent bg-clip-text">
          <Wrench className="h-6 w-6 text-primary" />
          مستلزمات الإنتاج للمشروع
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6 space-y-8">
        <RequirementsTable 
            title="1. الأجهزة والآلات والمعدات والأثاث المطلوبة عند التأسيس"
            example="ماكينة خياطة – عجانة – مولينكس – طاولات – كراسي – أرفف ..."
        />
        <RequirementsTable 
            title="2. المواد الخام المطلوبة لمدة شهر"
            example="طحين – قماش – صبغات – خيوط – أعلاف ..."
        />
      </CardContent>
    </Card>
  );
};