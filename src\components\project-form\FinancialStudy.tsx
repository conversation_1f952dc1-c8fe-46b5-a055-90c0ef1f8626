import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Calculator, PlusCircle, Trash2 } from "lucide-react";
import { useState, useMemo, FC, Fragment } from "react";
import { cn } from "@/lib/utils";

type CostItem = {
  name: string;
  monthly: number;
};

type ProfitRow = {
  id: number;
  name: string;
  units: number;
  costPerUnit: number;
  pricePerUnit: number;
};

type MonthlySalesData = {
  quantity: number;
  price: number;
};

type ProductSales = {
  id: number;
  name: string;
  monthlyData: MonthlySalesData[];
};

const SectionTitle = ({ title }: { title: string }) => (
    <h3 className="text-xl font-bold text-primary mt-8 mb-4">{title}</h3>
);

const MonthlyCostTable: FC<{
  title: string;
  costs: CostItem[];
  onCostChange: (index: number, value: string) => void;
  totalLabel: string;
  showRawMaterials?: boolean;
}> = ({ title, costs, onCostChange, totalLabel, showRawMaterials = false }) => {
  const totalMonthly = useMemo(() => costs.reduce((acc, cost) => acc + cost.monthly, 0), [costs]);
  const totalAnnual = totalMonthly * 12;

  const totalVariableWithoutRaw = useMemo(() => {
    if (!showRawMaterials) return 0;
    const rawMaterialCost = costs.find(c => c.name === "المواد الخام")?.monthly || 0;
    return totalMonthly - rawMaterialCost;
  }, [costs, totalMonthly, showRawMaterials]);

  return (
    <div className="space-y-2">
        <h4 className="font-semibold">{title}</h4>
        <div className="rounded-md border">
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead className="w-[50%]">البند</TableHead>
                        <TableHead>المبلغ الشهري</TableHead>
                        <TableHead className="text-left">المبلغ السنوي</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {costs.map((cost, index) => (
                        <TableRow key={index}>
                            <TableCell>{cost.name}</TableCell>
                            <TableCell>
                                <Input
                                    type="number"
                                    placeholder="0.00"
                                    value={cost.monthly || ""}
                                    onChange={(e) => onCostChange(index, e.target.value)}
                                    className="max-w-32"
                                />
                            </TableCell>
                            <TableCell className="text-left font-medium">{(cost.monthly * 12).toFixed(2)}</TableCell>
                        </TableRow>
                    ))}
                </TableBody>
                <TableFooter>
                    <TableRow className="bg-muted/50">
                        <TableCell className="font-bold">{totalLabel}</TableCell>
                        <TableCell className="font-bold">{totalMonthly.toFixed(2)}</TableCell>
                        <TableCell className="text-left font-bold">{totalAnnual.toFixed(2)}</TableCell>
                    </TableRow>
                    {showRawMaterials && (
                        <>
                            <TableRow>
                                <TableCell className="font-bold">🔸 مجموع التكاليف المتغيرة (بدون المواد الخام)</TableCell>
                                <TableCell className="font-bold">{totalVariableWithoutRaw.toFixed(2)}</TableCell>
                                <TableCell className="text-left font-bold">{(totalVariableWithoutRaw * 12).toFixed(2)}</TableCell>
                            </TableRow>
                            <TableRow className="bg-muted/50">
                                <TableCell className="font-bold">🔹 مجموع التكاليف المتغيرة (مع المواد الخام)</TableCell>
                                <TableCell className="font-bold">{totalMonthly.toFixed(2)}</TableCell>
                                <TableCell className="text-left font-bold">{totalAnnual.toFixed(2)}</TableCell>
                            </TableRow>
                        </>
                    )}
                </TableFooter>
            </Table>
        </div>
    </div>
  );
};

const SimpleCostTable = ({ title, items, totalLabel }: { title: string; items: string[]; totalLabel: string; }) => (
    <div className="space-y-2">
        <h4 className="font-semibold">{title}</h4>
        <div className="rounded-md border">
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead>البند</TableHead>
                        <TableHead className="text-left w-48">المبلغ بالدينار</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {items.map(item => (
                        <TableRow key={item}>
                            <TableCell>{item}</TableCell>
                            <TableCell className="text-left"><Input type="number" placeholder="0.00" /></TableCell>
                        </TableRow>
                    ))}
                </TableBody>
                <TableFooter>
                    <TableRow className="bg-muted/50">
                        <TableCell className="font-bold">{totalLabel}</TableCell>
                        <TableCell className="text-left font-bold">0.00</TableCell>
                    </TableRow>
                </TableFooter>
            </Table>
        </div>
    </div>
);


export const FinancialStudy = () => {
  const [fixedCosts, setFixedCosts] = useState<CostItem[]>([
    { name: "الرواتب الثابتة (صاحب المشروع + الموظفين الثابتين)", monthly: 0 },
    { name: "الإيجار", monthly: 0 },
    { name: "الصيانة", monthly: 0 },
    { name: "التسويق والدعاية", monthly: 0 },
    { name: "تكاليف ثابتة أخرى", monthly: 0 },
  ]);

  const [variableCosts, setVariableCosts] = useState<CostItem[]>([
    { name: "المواد الخام", monthly: 0 },
    { name: "أجور العمال المباشرين", monthly: 0 },
    { name: "فواتير الماء والكهرباء والهاتف والإنترنت", monthly: 0 },
    { name: "أجور النقل والمواصلات", monthly: 0 },
    { name: "تكاليف متغيرة أخرى", monthly: 0 },
  ]);

  const [profitRows, setProfitRows] = useState<ProfitRow[]>([
    { id: 1, name: "", units: 0, costPerUnit: 0, pricePerUnit: 0 },
  ]);

  const createInitialMonthlyData = () => Array.from({ length: 12 }, () => ({ quantity: 0, price: 0 }));

  const [annualSales, setAnnualSales] = useState<ProductSales[]>([
      { id: 1, name: "منتج (1)", monthlyData: createInitialMonthlyData() },
      { id: 2, name: "منتج (2)", monthlyData: createInitialMonthlyData() },
      { id: 3, name: "منتج (3)", monthlyData: createInitialMonthlyData() },
      { id: 4, name: "منتج (4)", monthlyData: createInitialMonthlyData() },
  ]);

  const [breakEvenInputs, setBreakEvenInputs] = useState({ salePrice: 0, variableCost: 0 });

  const handleFixedCostChange = (index: number, value: string) => {
    setFixedCosts(costs => {
      const newCosts = [...costs];
      newCosts[index] = { ...newCosts[index], monthly: Number(value) || 0 };
      return newCosts;
    });
  };

  const handleVariableCostChange = (index: number, value: string) => {
    setVariableCosts(costs => {
      const newCosts = [...costs];
      newCosts[index] = { ...newCosts[index], monthly: Number(value) || 0 };
      return newCosts;
    });
  };

  const handleProfitRowChange = (index: number, field: keyof Omit<ProfitRow, 'id'>, value: string) => {
    const newRows = [...profitRows];
    if (field === 'name') {
        newRows[index][field] = value;
    } else {
        // @ts-ignore
        newRows[index][field] = Number(value) || 0;
    }
    setProfitRows(newRows);
  };

  const addProfitRow = () => {
      setProfitRows(prevRows => [
          ...prevRows,
          { id: Date.now(), name: "", units: 0, costPerUnit: 0, pricePerUnit: 0 }
      ]);
  };

  const removeProfitRow = (id: number) => {
      setProfitRows(prevRows => prevRows.filter(row => row.id !== id));
  };

  const handleAnnualSalesChange = (productIndex: number, monthIndex: number, field: 'quantity' | 'price', value: string) => {
    setAnnualSales(currentSales => {
      const newSales = JSON.parse(JSON.stringify(currentSales));
      newSales[productIndex].monthlyData[monthIndex][field] = Number(value) || 0;
      return newSales;
    });
  };

  const totalFixedCostsMonthly = useMemo(() => fixedCosts.reduce((acc, cost) => acc + cost.monthly, 0), [fixedCosts]);
  const totalVariableCostsMonthly = useMemo(() => variableCosts.reduce((acc, cost) => acc + cost.monthly, 0), [variableCosts]);
  const totalOperatingCostsMonthly = totalFixedCostsMonthly + totalVariableCostsMonthly;

  const profitLossTotals = useMemo(() => {
    return profitRows.reduce((acc, row) => {
        const totalCost = row.units * row.costPerUnit;
        const totalRevenue = row.units * row.pricePerUnit;
        acc.totalCost += totalCost;
        acc.totalRevenue += totalRevenue;
        acc.totalProfit += (totalRevenue - totalCost);
        return acc;
    }, { totalCost: 0, totalRevenue: 0, totalProfit: 0 });
  }, [profitRows]);

  const monthlyTotals = useMemo(() => {
    const totals = Array(12).fill(0);
    annualSales.forEach(product => {
      product.monthlyData.forEach((data, monthIndex) => {
        totals[monthIndex] += data.quantity * data.price;
      });
    });
    return totals;
  }, [annualSales]);

  const totalAnnualRevenue = useMemo(() => monthlyTotals.reduce((acc, total) => acc + total, 0), [monthlyTotals]);

  const breakEvenPoint = useMemo(() => {
    const denominator = breakEvenInputs.salePrice - breakEvenInputs.variableCost;
    if (denominator <= 0) return 0;
    return totalFixedCostsMonthly / denominator;
  }, [totalFixedCostsMonthly, breakEvenInputs]);

  return (
    <Card className="w-full bg-purple-50 dark:bg-purple-900/30">
      <CardHeader className="border-b">
        <CardTitle className="flex items-center gap-3 text-2xl font-bold bg-gradient-to-r from-primary via-blue-500 to-sky-400 text-transparent bg-clip-text">
          <Calculator className="h-6 w-6 text-primary" />
          الدراسة المالية للمشروع
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        <SectionTitle title="💰 أولًا: رأس المال العامل – تكاليف المشروع التشغيلية" />
        <div className="space-y-6">
            <MonthlyCostTable 
                title="1. التكاليف الثابتة"
                costs={fixedCosts}
                onCostChange={handleFixedCostChange}
                totalLabel="🔹 مجموع التكاليف الثابتة"
            />
            <MonthlyCostTable 
                title="2. التكاليف المتغيرة"
                costs={variableCosts}
                onCostChange={handleVariableCostChange}
                totalLabel=""
                showRawMaterials={true}
            />
            <div className="p-4 bg-primary text-primary-foreground rounded-lg flex justify-between items-center">
                <span className="font-bold">✅ إجمالي رأس المال العامل (التكاليف التشغيلية)</span>
                <div className="text-right">
                    <p className="font-bold text-lg">{totalOperatingCostsMonthly.toFixed(2)} دينار / شهريًا</p>
                    <p className="font-bold text-sm">{(totalOperatingCostsMonthly * 12).toFixed(2)} دينار / سنويًا</p>
                </div>
            </div>
        </div>

        <Separator className="my-10" />

        <SectionTitle title="🏗️ ثانيًا: النفقات التأسيسية – ما قبل التشغيل" />
        <SimpleCostTable 
            title=""
            items={["التسجيل والترخيص", "توصيل الخدمات", "خلو / تجهيز الموقع", "تجهيز أولي", "أخرى"]}
            totalLabel="مجموع نفقات ما قبل التشغيل"
        />

        <Separator className="my-10" />

        <SectionTitle title="💼 ثالثًا: إجمالي رأس مال المشروع المتوقع" />
        <SimpleCostTable 
            title=""
            items={["رأس المال الثابت (تكاليف المشروع الرأسمالية – المعدات، الأجهزة...)", "رأس المال العامل (لأول شهر فقط)"]}
            totalLabel="🔷 إجمالي رأس مال المشروع المتوقع (مجموع النفقات والتكاليف)"
        />

        <Separator className="my-10" />

        <SectionTitle title="📊 رابعًا: حساب الربح والخسارة الشهري لكل منتج" />
        <div className="rounded-md border">
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead className="w-[20%]">المنتج / الخدمة</TableHead>
                        <TableHead>عدد الوحدات</TableHead>
                        <TableHead>تكلفة الوحدة</TableHead>
                        <TableHead>إجمالي التكلفة</TableHead>
                        <TableHead>سعر بيع الوحدة</TableHead>
                        <TableHead>إجمالي الإيراد</TableHead>
                        <TableHead>إجمالي الربح</TableHead>
                        <TableHead className="w-[5%]"></TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {profitRows.map((row, index) => {
                        const totalCost = row.units * row.costPerUnit;
                        const totalRevenue = row.units * row.pricePerUnit;
                        const totalProfit = totalRevenue - totalCost;
                        return (
                            <TableRow key={row.id}>
                                <TableCell><Input placeholder={`منتج ${index + 1}`} value={row.name} onChange={e => handleProfitRowChange(index, 'name', e.target.value)} /></TableCell>
                                <TableCell><Input type="number" placeholder="0" value={row.units || ''} onChange={e => handleProfitRowChange(index, 'units', e.target.value)} /></TableCell>
                                <TableCell><Input type="number" placeholder="0.00" value={row.costPerUnit || ''} onChange={e => handleProfitRowChange(index, 'costPerUnit', e.target.value)} /></TableCell>
                                <TableCell><Input readOnly value={totalCost.toFixed(2)} className="font-medium bg-muted" /></TableCell>
                                <TableCell><Input type="number" placeholder="0.00" value={row.pricePerUnit || ''} onChange={e => handleProfitRowChange(index, 'pricePerUnit', e.target.value)} /></TableCell>
                                <TableCell><Input readOnly value={totalRevenue.toFixed(2)} className="font-medium bg-muted" /></TableCell>
                                <TableCell><Input readOnly value={totalProfit.toFixed(2)} className={cn("font-bold bg-muted", totalProfit > 0 ? 'text-green-600' : totalProfit < 0 ? 'text-red-600' : '')} /></TableCell>
                                <TableCell>
                                    {profitRows.length > 1 && (
                                        <Button variant="ghost" size="icon" onClick={() => removeProfitRow(row.id)}>
                                            <Trash2 className="h-4 w-4 text-red-500" />
                                        </Button>
                                    )}
                                </TableCell>
                            </TableRow>
                        )
                    })}
                </TableBody>
                <TableFooter>
                    <TableRow className="bg-primary/10 font-bold text-lg">
                        <TableCell colSpan={3}>الإجمالي الشهري</TableCell>
                        <TableCell>{profitLossTotals.totalCost.toFixed(2)}</TableCell>
                        <TableCell></TableCell>
                        <TableCell>{profitLossTotals.totalRevenue.toFixed(2)}</TableCell>
                        <TableCell colSpan={2} className={cn(profitLossTotals.totalProfit > 0 ? 'text-green-700' : profitLossTotals.totalProfit < 0 ? 'text-red-700' : '')}>
                            {profitLossTotals.totalProfit.toFixed(2)}
                        </TableCell>
                    </TableRow>
                </TableFooter>
            </Table>
        </div>
        <div className="mt-4 flex justify-start">
            <Button onClick={addProfitRow} variant="outline">
                <PlusCircle className="ml-2 h-4 w-4" />
                إضافة منتج جديد
            </Button>
        </div>

        <Separator className="my-10" />

        <div className="space-y-4">
            <h3 className="text-xl font-bold text-white bg-red-800 p-2 rounded-md text-center">
            🧾 تقدير الإيرادات / المبيعات لسنة
            </h3>
            <div className="rounded-md border overflow-x-auto">
                <Table className="min-w-[1200px]">
                    <TableHeader>
                        <TableRow>
                            <TableHead rowSpan={2} className="w-[200px] sticky right-0 bg-muted align-bottom text-center">المبيعات / الإيرادات</TableHead>
                            <TableHead colSpan={12} className="text-center bg-muted/50">أشهر السنة الأولى</TableHead>
                            <TableHead rowSpan={2} className="w-[120px] sticky left-0 bg-muted align-bottom text-center">المجموع السنوي</TableHead>
                        </TableRow>
                        <TableRow>
                            {Array.from({ length: 12 }, (_, i) => (
                                <TableHead key={i} className="text-center bg-muted/50">{i + 1}</TableHead>
                            ))}
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {annualSales.map((product, productIndex) => {
                            const totalProductRevenue = product.monthlyData.reduce((acc, data) => acc + (data.quantity * data.price), 0);
                            const totalProductQuantity = product.monthlyData.reduce((acc, data) => acc + data.quantity, 0);
                            return (
                                <Fragment key={product.id}>
                                    <TableRow className="bg-primary/5">
                                        <TableCell colSpan={14} className="font-bold sticky right-0 bg-primary/5">{product.name}</TableCell>
                                    </TableRow>
                                    <TableRow>
                                        <TableCell className="font-medium sticky right-0 bg-background">الكمية المتوقع بيعها</TableCell>
                                        {product.monthlyData.map((_, monthIndex) => (
                                            <TableCell key={monthIndex}>
                                                <Input
                                                    type="number"
                                                    placeholder="0"
                                                    className="min-w-[65px] text-center"
                                                    value={annualSales[productIndex].monthlyData[monthIndex].quantity || ''}
                                                    onChange={(e) => handleAnnualSalesChange(productIndex, monthIndex, 'quantity', e.target.value)}
                                                />
                                            </TableCell>
                                        ))}
                                        <TableCell className="text-center font-bold bg-muted sticky left-0">
                                            {totalProductQuantity}
                                        </TableCell>
                                    </TableRow>
                                    <TableRow>
                                        <TableCell className="font-medium sticky right-0 bg-background">سعر البيع للوحدة</TableCell>
                                        {product.monthlyData.map((_, monthIndex) => (
                                            <TableCell key={monthIndex}>
                                                <Input
                                                    type="number"
                                                    placeholder="0.00"
                                                    className="min-w-[65px] text-center"
                                                    value={annualSales[productIndex].monthlyData[monthIndex].price || ''}
                                                    onChange={(e) => handleAnnualSalesChange(productIndex, monthIndex, 'price', e.target.value)}
                                                />
                                            </TableCell>
                                        ))}
                                        <TableCell className="text-center font-bold bg-muted sticky left-0">-</TableCell>
                                    </TableRow>
                                    <TableRow className="bg-muted/20">
                                        <TableCell className="font-medium sticky right-0 bg-muted/20">الإيراد (الكمية × السعر)</TableCell>
                                        {product.monthlyData.map((data, monthIndex) => (
                                            <TableCell key={monthIndex} className="text-center font-semibold">
                                                {(data.quantity * data.price).toFixed(2)}
                                            </TableCell>
                                        ))}
                                        <TableCell className="text-center font-bold bg-muted/50 sticky left-0">
                                            {totalProductRevenue.toFixed(2)}
                                        </TableCell>
                                    </TableRow>
                                </Fragment>
                            )
                        })}
                    </TableBody>
                    <TableFooter>
                        <TableRow className="bg-primary text-primary-foreground">
                            <TableCell className="font-bold text-lg sticky right-0 bg-primary">
                                مجموع الإيرادات الشهرية
                            </TableCell>
                            {monthlyTotals.map((total, index) => (
                                <TableCell key={index} className="text-center font-bold">
                                    {total.toFixed(2)}
                                </TableCell>
                            ))}
                            <TableCell className="text-center font-bold text-lg sticky left-0 bg-primary">
                                {totalAnnualRevenue.toFixed(2)}
                            </TableCell>
                        </TableRow>
                    </TableFooter>
                </Table>
            </div>
        </div>

        <Separator className="my-10" />

        <SectionTitle title="📍 خامسًا: حساب نقطة التعادل (Break-even Point)" />
        <p className="text-sm text-muted-foreground">هي عدد الوحدات التي يجب بيعها لتغطية جميع التكاليف (الثابتة + المتغيرة)، بحيث لا يوجد ربح ولا خسارة.</p>
        <p className="text-sm text-muted-foreground font-mono p-2 bg-muted rounded-md">نقطة التعادل (بالوحدات) = التكاليف الثابتة الشهرية ÷ (سعر بيع الوحدة – تكلفة الوحدة المتغيرة)</p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div className="space-y-2">
                <Label>التكاليف الثابتة الشهرية</Label>
                <Input type="number" value={totalFixedCostsMonthly.toFixed(2)} readOnly disabled />
            </div>
            <div className="space-y-2">
                <Label>متوسط سعر بيع الوحدة</Label>
                <Input type="number" placeholder="0.00" value={breakEvenInputs.salePrice || ""} onChange={e => setBreakEvenInputs(s => ({...s, salePrice: Number(e.target.value)}))} />
            </div>
            <div className="space-y-2">
                <Label>متوسط تكلفة الوحدة المتغيرة</Label>
                <Input type="number" placeholder="0.00" value={breakEvenInputs.variableCost || ""} onChange={e => setBreakEvenInputs(s => ({...s, variableCost: Number(e.target.value)}))} />
            </div>
            <div className="p-4 bg-primary text-primary-foreground rounded-lg flex justify-between items-center md:col-span-2">
                <span className="font-bold">🔹 نقطة التعادل (عدد الوحدات)</span>
                <span className="font-bold text-lg">{breakEvenPoint.toFixed(2)} وحدة</span>
            </div>
        </div>

      </CardContent>
    </Card>
  );
};