// أنواع البيانات للمشروع
export interface PersonalInfo {
  ownerName: string;
  age: number;
  maritalStatus: string;
  familySize: number;
  education: string;
  phone: string;
  refereePhone: string;
  address: string;
}

export interface ProjectDescription {
  projectSummary: string;
  projectName: string;
  projectLocation: string;
  projectType: string;
  projectGoals: string;
  targetMarket: string;
  expectedRevenue: number;
  startupCost: number;
}

export interface MarketStudy {
  products: string;
  services: string;
  hasCompetitors: boolean;
  competitorsCount: number;
  competitorProducts: string;
  competitorProfitStrategies: string[];
  competitorPricing: 'same' | 'higher' | 'lower';
  competitorPromotionMethods: string[];
  competitor1Customers: number;
  competitor2Customers: number;
}

export interface SwotAnalysis {
  strengths: string;
  weaknesses: string;
  opportunities: string;
  threats: string;
}

export interface MarketingMix {
  product: string;
  price: string;
  place: string;
  promotion: string;
  people: string;
}

export interface ProductionRequirements {
  equipment: string;
  materials: string;
  humanResources: string;
  location: string;
  licenses: string;
}

export interface CostItem {
  name: string;
  monthly: number;
}

export interface ProfitRow {
  id: number;
  name: string;
  units: number;
  costPerUnit: number;
  pricePerUnit: number;
}

export interface MonthlyData {
  quantity: number;
  price: number;
}

export interface ProductSales {
  id: number;
  name: string;
  monthlyData: MonthlyData[];
}

export interface BreakEvenInputs {
  salePrice: number;
  variableCost: number;
}

export interface FinancialStudy {
  fixedCosts: CostItem[];
  variableCosts: CostItem[];
  profitRows: ProfitRow[];
  annualSales: ProductSales[];
  breakEvenInputs: BreakEvenInputs;
}

export interface ProjectData {
  personalInfo: PersonalInfo;
  projectDescription: ProjectDescription;
  marketStudy: MarketStudy;
  swotAnalysis: SwotAnalysis;
  marketingMix: MarketingMix;
  productionRequirements: ProductionRequirements;
  financialStudy: FinancialStudy;
  lastUpdated: Date;
  currentStep: number;
}

export const initialProjectData: ProjectData = {
  personalInfo: {
    ownerName: '',
    age: 0,
    maritalStatus: '',
    familySize: 0,
    education: '',
    phone: '',
    refereePhone: '',
    address: '',
  },
  projectDescription: {
    projectSummary: '',
    projectName: '',
    projectLocation: '',
    projectType: '',
    projectGoals: '',
    targetMarket: '',
    expectedRevenue: 0,
    startupCost: 0,
  },
  marketStudy: {
    products: '',
    services: '',
    hasCompetitors: false,
    competitorsCount: 0,
    competitorProducts: '',
    competitorProfitStrategies: [],
    competitorPricing: 'same',
    competitorPromotionMethods: [],
    competitor1Customers: 0,
    competitor2Customers: 0,
  },
  swotAnalysis: {
    strengths: '',
    weaknesses: '',
    opportunities: '',
    threats: '',
  },
  marketingMix: {
    product: '',
    price: '',
    place: '',
    promotion: '',
    people: '',
  },
  productionRequirements: {
    equipment: '',
    materials: '',
    humanResources: '',
    location: '',
    licenses: '',
  },
  financialStudy: {
    fixedCosts: [
      { name: "الرواتب الثابتة (صاحب المشروع + الموظفين الثابتين)", monthly: 0 },
      { name: "الإيجار", monthly: 0 },
      { name: "الصيانة", monthly: 0 },
      { name: "التسويق والدعاية", monthly: 0 },
      { name: "تكاليف ثابتة أخرى", monthly: 0 },
    ],
    variableCosts: [
      { name: "المواد الخام", monthly: 0 },
      { name: "أجور العمال المباشرين", monthly: 0 },
      { name: "فواتير الماء والكهرباء والهاتف والإنترنت", monthly: 0 },
      { name: "أجور النقل والمواصلات", monthly: 0 },
      { name: "تكاليف متغيرة أخرى", monthly: 0 },
    ],
    profitRows: [
      { id: 1, name: "", units: 0, costPerUnit: 0, pricePerUnit: 0 },
    ],
    annualSales: [
      { id: 1, name: "منتج (1)", monthlyData: Array.from({ length: 12 }, () => ({ quantity: 0, price: 0 })) },
      { id: 2, name: "منتج (2)", monthlyData: Array.from({ length: 12 }, () => ({ quantity: 0, price: 0 })) },
      { id: 3, name: "منتج (3)", monthlyData: Array.from({ length: 12 }, () => ({ quantity: 0, price: 0 })) },
      { id: 4, name: "منتج (4)", monthlyData: Array.from({ length: 12 }, () => ({ quantity: 0, price: 0 })) },
    ],
    breakEvenInputs: {
      salePrice: 0,
      variableCost: 0,
    },
  },
  lastUpdated: new Date(),
  currentStep: 0,
};
