import { cn } from "@/lib/utils";
import { useProject } from "@/contexts/ProjectContext";
import { CheckCircle, Circle, AlertCircle } from "lucide-react";

interface FormProgressProps {
  currentStep: number;
  totalSteps: number;
}

export const FormProgress = ({ currentStep, totalSteps }: FormProgressProps) => {
  const { isDataValid } = useProject();
  const progressPercentage = ((currentStep + 1) / totalSteps) * 100;

  const stepNames = [
    "المعلومات الشخصية",
    "دراسة السوق",
    "تحليل SWOT",
    "المزيج التسويقي",
    "مستلزمات الإنتاج",
    "الدراسة المالية",
    "الإنهاء والحفظ"
  ];

  return (
    <div className="w-full space-y-4">
      <div className="flex justify-between mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
        <span>التقدم</span>
        <span>الخطوة {currentStep + 1}/{totalSteps}</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
        <div
          className="bg-primary h-2.5 rounded-full transition-all duration-500"
          style={{ width: `${progressPercentage}%` }}
        ></div>
      </div>

      {/* عرض حالة الخطوات */}
      <div className="hidden md:flex justify-between items-center text-xs">
        {stepNames.map((stepName, index) => {
          const isCompleted = index < currentStep;
          const isCurrent = index === currentStep;
          const isValid = isDataValid(index);

          return (
            <div key={index} className="flex flex-col items-center space-y-1">
              <div className={cn(
                "flex items-center justify-center w-8 h-8 rounded-full border-2 transition-colors",
                isCompleted ? "bg-green-500 border-green-500 text-white" :
                isCurrent ? (isValid ? "bg-primary border-primary text-white" : "bg-amber-500 border-amber-500 text-white") :
                "bg-gray-200 border-gray-300 text-gray-500"
              )}>
                {isCompleted ? (
                  <CheckCircle className="w-4 h-4" />
                ) : isCurrent && !isValid ? (
                  <AlertCircle className="w-4 h-4" />
                ) : (
                  <Circle className="w-4 h-4" />
                )}
              </div>
              <span className={cn(
                "text-center max-w-16 leading-tight",
                isCurrent ? "text-primary font-medium" : "text-gray-500"
              )}>
                {stepName}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
};