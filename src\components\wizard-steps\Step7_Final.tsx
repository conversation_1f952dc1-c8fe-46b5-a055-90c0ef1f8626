import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { FileDown, FileText, Sheet, CheckCircle, Calendar, User } from "lucide-react";
import { useProject } from "@/contexts/ProjectContext";
import { toast } from "@/hooks/use-toast";

export const Step7_Final = () => {
  const { projectData, saveProject, resetProject } = useProject();

  const handleSaveProgress = () => {
    saveProject();
    toast({
      title: "تم الحفظ بنجاح!",
      description: "تم حفظ جميع بيانات المشروع في التخزين المحلي",
    });
  };

  const handleExportPDF = () => {
    // TODO: تنفيذ تصدير PDF
    toast({
      title: "قريباً",
      description: "ميزة تصدير PDF ستكون متاحة قريباً",
      variant: "destructive",
    });
  };

  const handleExportExcel = () => {
    // TODO: تنفيذ تصدير Excel
    toast({
      title: "قريباً",
      description: "ميزة تصدير Excel ستكون متاحة قريباً",
      variant: "destructive",
    });
  };

  const handleReset = () => {
    if (confirm("هل أنت متأكد من أنك تريد مسح جميع البيانات؟")) {
      resetProject();
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <div className="space-y-6">
      <div className="text-center p-8 space-y-6">
        <div className="flex justify-center">
          <CheckCircle className="h-16 w-16 text-green-500" />
        </div>
        <h2 className="text-3xl font-bold text-green-600">تهانينا! لقد أكملت خطة المشروع!</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          عمل رائع! لقد قمت بإنشاء خطة عمل شاملة لمشروعك. يمكنك الآن حفظ خطة عملك أو تصديرها كملف PDF أو Excel.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              ملخص المشروع
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div><strong>اسم المشروع:</strong> {projectData.projectDescription.projectName || "غير محدد"}</div>
            <div><strong>صاحب المشروع:</strong> {projectData.personalInfo.ownerName || "غير محدد"}</div>
            <div><strong>موقع المشروع:</strong> {projectData.projectDescription.projectLocation || "غير محدد"}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              معلومات الحفظ
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div><strong>آخر تحديث:</strong> {formatDate(projectData.lastUpdated)}</div>
            <div><strong>الخطوة الحالية:</strong> {projectData.currentStep + 1} من 7</div>
          </CardContent>
        </Card>
      </div>

      <div className="flex flex-wrap justify-center gap-4 pt-4">
        <Button size="lg" variant="outline" onClick={handleSaveProgress}>
          <FileDown className="ml-2 h-5 w-5" />
          حفظ التقدم
        </Button>
        <Button size="lg" onClick={handleExportPDF}>
          <FileText className="ml-2 h-5 w-5" />
          تصدير PDF
        </Button>
        <Button size="lg" variant="secondary" onClick={handleExportExcel}>
          <Sheet className="ml-2 h-5 w-5" />
          تصدير Excel
        </Button>
        <Button size="lg" variant="destructive" onClick={handleReset}>
          مسح البيانات
        </Button>
      </div>
    </div>
  );
};