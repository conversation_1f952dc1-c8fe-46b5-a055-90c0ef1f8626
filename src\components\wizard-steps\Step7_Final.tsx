import { Button } from "@/components/ui/button";
import { FileDown, FileText, Sheet } from "lucide-react";

export const Step7_Final = () => {
  return (
    <div className="text-center p-8 space-y-6">
      <h2 className="text-3xl font-bold">لقد أكملت خطة المشروع!</h2>
      <p className="text-muted-foreground">
        عمل رائع! يمكنك الآن حفظ خطة عملك أو تصديرها كملف PDF أو Excel.
      </p>
      <div className="flex justify-center gap-4 pt-4">
        <Button size="lg" variant="outline">
          <FileDown className="ml-2 h-5 w-5" />
          حفظ التقدم
        </Button>
        <Button size="lg">
          <FileText className="ml-2 h-5 w-5" />
          تصدير PDF
        </Button>
        <Button size="lg" variant="secondary">
          <Sheet className="ml-2 h-5 w-5" />
          تصدير Excel
        </Button>
      </div>
    </div>
  );
};