import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { BarChart4 } from "lucide-react";

const RadioField = ({ label, id, value }: { label: string; id: string; value: string; }) => (
    <Label htmlFor={id} className="font-normal flex items-center justify-start gap-2 cursor-pointer">
        <RadioGroupItem value={value} id={id} />
        <span>{label}</span>
    </Label>
);

const CheckboxField = ({ label, id }: { label: string; id: string; }) => (
    <Label htmlFor={id} className="font-normal flex items-center justify-start gap-2 cursor-pointer">
        <Checkbox id={id} />
        <span>{label}</span>
    </Label>
);

export const MarketStudyP2 = () => {
  return (
    <Card className="w-full bg-emerald-50 dark:bg-emerald-900/30">
      <CardHeader className="border-b">
        <CardTitle className="flex items-center gap-3 text-2xl font-bold bg-gradient-to-r from-primary via-blue-500 to-sky-400 text-transparent bg-clip-text">
          <BarChart4 className="h-6 w-6 text-primary" />
          دراسة السوق والمنافسين – الجزء الثاني
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6 space-y-8">
        <div className="space-y-3">
            <Label htmlFor="potentialCustomers" className="text-base font-semibold">كم أعداد الزبائن المحتمل أن يشتروا منتجاتك (العدد الكلي للزبائن المحتملين)</Label>
            <div className="pr-4">
                <Input type="number" id="potentialCustomers" />
            </div>
        </div>
        
        <div className="space-y-3">
            <Label htmlFor="consumptionRate" className="text-base font-semibold">كم معدل استهلاكهم للمنتج في الشهر</Label>
            <div className="pr-4">
                <Input type="number" id="consumptionRate" />
            </div>
        </div>
        
        <div className="space-y-3">
            <Label className="text-base font-semibold">هل المنتجات الشبيهة تلبي احتياج السوق أم أن هناك طلبًا كبيرًا على المنتجات والمعروض أقل:</Label>
            <div className="pr-4">
                <RadioGroup className="space-y-2">
                    <RadioField label="هناك طلب كبير على المنتجات والمعروض أقل" id="demandHigh" value="high" />
                    <RadioField label="المعروض من المنتجات الشبيهة أكبر من الطلب" id="supplyHigh" value="supply_high" />
                    <RadioField label="المعروض في السوق هو نفس حاجة السوق (العرض = الطلب)" id="demandEqual" value="equal" />
                </RadioGroup>
            </div>
        </div>

        <div className="space-y-3">
            <Label htmlFor="peakSeasons" className="text-base font-semibold">ما هي المواسم التي يشتد فيها البيع لمنتجاتك أو خدماتك (مثل: الأعياد، رمضان، الصيف...)</Label>
            <div className="pr-4">
                <Textarea id="peakSeasons" />
            </div>
        </div>

        <div className="space-y-3">
            <Label className="text-base font-semibold">بماذا سيتميز (يتفوق) منتجك عن المنتجات الشبيهة في السوق؟</Label>
            <div className="pr-4 space-y-2">
                <CheckboxField label="السعر المنخفض" id="advantagePrice" />
                <CheckboxField label="الجودة العالية" id="advantageQuality" />
                <CheckboxField label="الخدمة المميزة" id="advantageService" />
                <CheckboxField label="التكلفة المنخفضة" id="advantageCost" />
                <div className="flex items-center gap-2 w-full max-w-sm">
                    <CheckboxField label="أخرى (اذكرها):" id="advantageOther" />
                    <Input id="advantageOtherText" className="flex-1 h-8" />
                </div>
            </div>
        </div>

        <div className="space-y-3">
            <Label className="text-base font-semibold">كيف ستقوم ببيع وتسويق منتجك؟</Label>
            <div className="pr-4 space-y-2">
                <CheckboxField label="من خلال الإنترنت ووسائل التواصل الاجتماعي" id="marketingSocial" />
                <CheckboxField label="من خلال البيع المباشر للناس" id="marketingDirect" />
                <CheckboxField label="من خلال زيارة الزبائن والترويج للمنتج" id="marketingVisits" />
                <div className="flex items-center gap-2 w-full max-w-sm">
                    <CheckboxField label="أخرى (اذكرها):" id="marketingOther" />
                    <Input id="marketingOtherText" className="flex-1 h-8" />
                </div>
            </div>
        </div>

        <div className="space-y-3">
            <Label className="text-base font-semibold">هل سيحتاج مشروعك إلى موردين أو مزودين للمواد الخام (الأولية) أو الخدمات؟</Label>
            <div className="pr-4">
                <RadioGroup defaultValue="no" className="flex items-center gap-x-6">
                    <div className="flex items-center gap-2">
                        <RadioField label="نعم – كم عددهم؟" id="suppliersYes" value="yes" />
                        <Input id="suppliersCount" type="number" className="w-24 h-8" />
                    </div>
                    <RadioField label="لا" id="suppliersNo" value="no" />
                </RadioGroup>
            </div>
        </div>

        <div className="space-y-3">
            <Label className="text-base font-semibold">هل المزودين أو الموردين سهل الوصول إليهم؟</Label>
            <div className="pr-4">
                <RadioGroup className="space-y-2">
                    <div className="flex items-center gap-2 w-full max-w-md">
                        <RadioField label="نعم (كيف؟)" id="suppliersAccessYes" value="yes" />
                        <Input id="suppliersAccessYesText" className="flex-1 h-8" />
                    </div>
                    <div className="flex items-center gap-2 w-full max-w-md">
                        <RadioField label="لا (لماذا؟)" id="suppliersAccessNo" value="no" />
                        <Input id="suppliersAccessNoText" className="flex-1 h-8" />
                    </div>
                </RadioGroup>
            </div>
        </div>

        <div className="space-y-3">
            <Label className="text-base font-semibold">هل أسعار المزودين أو الموردين مناسبة؟</Label>
            <div className="pr-4">
                <RadioGroup className="space-y-2">
                    <RadioField label="مناسبة" id="supplierPriceGood" value="good" />
                    <RadioField label="مرتفعة" id="supplierPriceHigh" value="high" />
                    <RadioField label="منخفضة" id="supplierPriceLow" value="low" />
                    <RadioField label="متذبذبة حسب السوق" id="supplierPriceFluctuates" value="fluctuates" />
                </RadioGroup>
            </div>
        </div>
      </CardContent>
    </Card>
  );
};